# 创建视频任务
## 可携带多个图片或者不携带，图片可以是base64
https://yunwu.ai/v1/video/create
{
    "prompt": "牛飞上天了",
    "model": "veo3",
    "images": [
        "https://filesystem.site/cdn/20250612/VfgB5ubjInVt8sG6rzMppxnu7gEfde.png",
        "https://filesystem.site/cdn/20250612/998IGmUiM2koBGZM3UnZeImbPBNIUL.png"
    ],
    "enhance_prompt": true
}
## 接口响应 id是用来查询任务是否结束
{
    "id": "veo3:1754363639-1CZvGZBkkH",
    "status": "pending",
    "status_update_time": 1754363639575
}

# 查询任务结果
## 使用响应内的id
https://yunwu.ai/v1/video/query?id=veo3:1753613210-OR998GOIzu
## 接口响应
{
    "id": "veo3:1754363639-1CZvGZBkkH",
    "detail": {
        "id": "veo3:1754363639-1CZvGZBkkH",
        "req": {
            "model": "veo3",
            "images": [
                "https://filesystem.site/cdn/20250612/VfgB5ubjInVt8sG6rzMppxnu7gEfde.png",
                "https://filesystem.site/cdn/20250612/998IGmUiM2koBGZM3UnZeImbPBNIUL.png"
            ],
            "prompt": "牛飞上天了",
            "enhance_prompt": true,
            "enable_upsample": false
        },
        "images": [
            {
                "url": "https://filesystem.site/cdn/20250612/VfgB5ubjInVt8sG6rzMppxnu7gEfde.png",
                "status": "completed",
                "mediaId": "CAMaJDkzYWMzM2ZlLTk0MmQtNGJkNi1hODY0LTg3NWYzNGRmNTYyMSIDQ0FFKiQ5MzVkZDQ3My05ZTMyLTQ5ZTctOWRkNi0yZjk5ODJmYmYyOWY"
            },
            {
                "url": "https://filesystem.site/cdn/20250612/998IGmUiM2koBGZM3UnZeImbPBNIUL.png",
                "status": "completed",
                "mediaId": "CAMaJGFhNGI1ZTUwLWFkZGMtNGRkOC1iNWM5LTY0MTMzNjIyMGY3YiIDQ0FFKiRmMWVlZDZhZS1iMDY5LTQ3NDAtYTUyNS1jMDA3NDU0YzZmYTI"
            }
        ],
        "status": "completed",
        "running": false,
        "video_url": "https://filesystem.site/cdn/20250805/ux2EoFgqbCxrlo6SJ8jGEmyxRH3ckg.mp4",
        "created_at": 1754363639575,
        "max_retries": 3,
        "retry_count": 0,
        "completed_at": 1754363728338,
        "video_media_id": "CAUSJGIxZDk2MTFhLTcxMzgtNDhmYi1hNWFmLTI1NmJhMWE0MjdkNxokNjIzMmFiMTItZDM4Yy00NjBlLTkyYWItMDgwMTVkNjdiZWE5IgNDQUUqJDZjMDY0M2U4LTYxNjctNDQ5Ny1hNjUwLTg1OWUzNDIzYzY4YQ",
        "enhanced_prompt": "The ox flew up to the sky",
        "status_update_time": 1754363728438,
        "video_generation_id": "93efc0854c876109e0cf08d925a8e5b7",
        "video_generation_status": "MEDIA_GENERATION_STATUS_SUCCESSFUL"
    },
    "status": "completed",
    "video_url": "https://filesystem.site/cdn/20250805/ux2EoFgqbCxrlo6SJ8jGEmyxRH3ckg.mp4",
    "status_update_time": 1754363728438
}